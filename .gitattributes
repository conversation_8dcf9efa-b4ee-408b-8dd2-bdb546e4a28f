# Auto detect text files and perform LF normalization
* text=auto

# JavaScript files
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf

# JSON files
*.json text eol=lf

# CSS files
*.css text eol=lf
*.scss text eol=lf
*.sass text eol=lf

# HTML files
*.html text eol=lf

# Markdown files
*.md text eol=lf

# Config files
*.yml text eol=lf
*.yaml text eol=lf
*.toml text eol=lf
*.ini text eol=lf

# Batch files (Windows)
*.bat text eol=crlf
*.cmd text eol=crlf

# Images
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text eol=lf

# Fonts
*.woff binary
*.woff2 binary
*.eot binary
*.ttf binary
*.otf binary
