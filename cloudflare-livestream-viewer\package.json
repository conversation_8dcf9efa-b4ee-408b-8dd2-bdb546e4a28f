{"name": "cloudflare-livestream-viewer", "version": "1.0.0", "description": "React application to view live and ready videos from Cloudflare Stream", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start", "deploy": "npm run build"}, "keywords": ["react", "cloudflare", "stream", "livestream", "video", "viewer"], "author": "Your Name", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": ".", "repository": {"type": "git", "url": "https://github.com/yourusername/cloudflare-livestream-viewer.git"}, "bugs": {"url": "https://github.com/yourusername/cloudflare-livestream-viewer/issues"}}