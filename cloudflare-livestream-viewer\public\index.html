<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#667eea" />
    <meta name="description" content="Cloudflare Stream Livestream Viewer - View live and ready videos from Cloudflare Stream" />
    <meta name="keywords" content="cloudflare, stream, livestream, video, viewer, react" />
    <meta name="author" content="Your Name" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Cloudflare Stream Livestream Viewer" />
    <meta property="og:description" content="View live and ready videos from Cloudflare Stream with responsive grid layout" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Cloudflare Stream Livestream Viewer" />
    <meta property="twitter:description" content="View live and ready videos from Cloudflare Stream with responsive grid layout" />
    <meta property="twitter:image" content="%PUBLIC_URL%/og-image.png" />
    
    <title>Cloudflare Stream Livestream Viewer</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- 
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.
      
      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.
      
      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
