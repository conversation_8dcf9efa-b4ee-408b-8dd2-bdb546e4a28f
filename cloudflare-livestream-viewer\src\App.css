.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App-main {
  flex: 1;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* About Section */
.about-section {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  color: white;
  text-align: center;
}

.about-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: left;
  margin-top: 20px;
}

.about-section h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.about-content h3 {
  color: #fff;
  font-size: 1.3rem;
  margin: 25px 0 15px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.about-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 20px;
}

.about-content ul {
  list-style: none;
  padding: 0;
}

.about-content li {
  padding: 8px 0;
  font-size: 1rem;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 10px;
}

.about-content li:before {
  content: "→";
  color: #ff6b6b;
  font-weight: bold;
  margin-right: 5px;
}

/* Responsive */
@media (max-width: 768px) {
  .App-main {
    padding: 15px;
  }
  
  .about-section {
    padding: 20px 15px;
  }
  
  .about-section h1 {
    font-size: 2rem;
  }
  
  .about-content {
    padding: 20px;
  }
  
  .about-content h3 {
    font-size: 1.2rem;
  }
  
  .about-content p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .about-section h1 {
    font-size: 1.8rem;
  }
  
  .about-content {
    padding: 15px;
  }
}
