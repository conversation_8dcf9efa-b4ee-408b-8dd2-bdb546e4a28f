import React, { useState } from 'react';
import './App.css';
import LivestreamViewer from './components/LivestreamViewer';
import Header from './components/Header';
import Footer from './components/Footer';

function App() {
  const [currentView, setCurrentView] = useState('livestream');

  const renderView = () => {
    switch(currentView) {
      case 'livestream':
        return <LivestreamViewer />;
      case 'about':
        return (
          <div className="about-section">
            <h1>📺 Về Cloudflare Stream Viewer</h1>
            <div className="about-content">
              <p>
                Ứng dụng này giúp bạn xem tất cả video livestream từ Cloudflare Stream 
                với giao diện đẹp mắt và responsive.
              </p>
              <h3>✨ Tính năng:</h3>
              <ul>
                <li>🔴 Xem video đang live (live-inprogress)</li>
                <li>✅ Xem video sẵn sàng (ready)</li>
                <li>🔄 Tự động refresh mỗi 30 giây</li>
                <li>📱 Responsive trên mọi thiết bị</li>
                <li>🎛️ Toggle giữa các chế độ xem</li>
              </ul>
              <h3>🛠️ Công nghệ:</h3>
              <ul>
                <li>React 18 với Hooks</li>
                <li>Cloudflare Stream API</li>
                <li>CSS Grid Layout</li>
                <li>Modern JavaScript (ES6+)</li>
              </ul>
            </div>
          </div>
        );
      default:
        return <LivestreamViewer />;
    }
  };

  return (
    <div className="App">
      <Header currentView={currentView} setCurrentView={setCurrentView} />
      
      <main className="App-main">
        {renderView()}
      </main>
      
      <Footer />
    </div>
  );
}

export default App;
