import React from 'react';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="app-footer">
      <div className="footer-container">
        <div className="footer-content">
          <div className="footer-section">
            <h3>🔴 Cloudflare Stream Viewer</h3>
            <p>Xem livestream và video từ Cloudflare Stream với giao diện hiện đại</p>
          </div>
          
          <div className="footer-section">
            <h4>Tính năng</h4>
            <ul>
              <li>Live video streaming</li>
              <li>Responsive grid layout</li>
              <li>Auto-refresh</li>
              <li>Multiple video states</li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4>Công nghệ</h4>
            <ul>
              <li>React 18</li>
              <li>Cloudflare Stream API</li>
              <li>CSS Grid</li>
              <li>Modern JavaScript</li>
            </ul>
          </div>
          
          <div className="footer-section">
            <h4><PERSON><PERSON><PERSON> kết</h4>
            <ul>
              <li>
                <a href="https://developers.cloudflare.com/stream/" target="_blank" rel="noopener noreferrer">
                  📚 Cloudflare Stream Docs
                </a>
              </li>
              <li>
                <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                  💻 GitHub Repository
                </a>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="footer-bottom">
          <p>&copy; {currentYear} Cloudflare Stream Viewer. Made with ❤️ using React</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
