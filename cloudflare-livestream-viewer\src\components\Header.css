.app-header {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 15px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.logo h1 {
  color: #fff;
  font-size: 1.8rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin: 5px 0 0 0;
  font-style: italic;
}

.header-nav {
  display: flex;
  gap: 15px;
  align-items: center;
}

.nav-button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.nav-button.active {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-color: #ff6b6b;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.nav-button.active:hover {
  background: linear-gradient(45deg, #ee5a24, #ff6b6b);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Responsive */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .logo h1 {
    font-size: 1.5rem;
  }
  
  .header-nav {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .nav-button {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 15px;
  }
  
  .logo h1 {
    font-size: 1.3rem;
  }
  
  .nav-button {
    padding: 8px 14px;
    font-size: 0.85rem;
  }
}
