import React from 'react';
import './Header.css';

const Header = ({ currentView, setCurrentView }) => {
  return (
    <header className="app-header">
      <div className="header-container">
        <div className="logo">
          <h1>🔴 Cloudflare Stream Viewer</h1>
          <p>Live & Ready Videos</p>
        </div>
        
        <nav className="header-nav">
          <button 
            className={`nav-button ${currentView === 'livestream' ? 'active' : ''}`}
            onClick={() => setCurrentView('livestream')}
          >
            📺 Livestream
          </button>
          <button 
            className={`nav-button ${currentView === 'about' ? 'active' : ''}`}
            onClick={() => setCurrentView('about')}
          >
            ℹ️ About
          </button>
        </nav>
      </div>
    </header>
  );
};

export default Header;
