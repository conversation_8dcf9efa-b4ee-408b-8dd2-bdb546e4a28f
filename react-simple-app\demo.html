<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Simple App - Demo</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        * {
            box-sizing: border-box;
        }

        .App {
            text-align: center;
        }

        .App-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px;
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: calc(10px + 2vmin);
        }

        .App-header h1 {
            margin-bottom: 20px;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .App-header p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .counter-section, .input-section, .info-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
        }

        .counter-section h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #fff;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .button-group button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .button-group button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
        }

        .button-group button:active {
            transform: translateY(0);
        }

        .input-section h2 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #fff;
        }

        .input-section input {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            width: 250px;
            max-width: 100%;
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .input-section input:focus {
            outline: none;
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            transform: scale(1.05);
        }

        .input-section input::placeholder {
            color: #666;
        }

        .info-section {
            font-size: 1rem;
        }

        .info-section p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .info-section ul {
            list-style: none;
            padding: 0;
            text-align: left;
            max-width: 300px;
            margin: 0 auto;
        }

        .info-section li {
            padding: 8px 0;
            font-size: 1rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .App-header {
                padding: 20px 10px;
                font-size: calc(8px + 2vmin);
            }
            
            .App-header h1 {
                font-size: 2rem;
            }
            
            .button-group {
                flex-direction: column;
                align-items: center;
            }
            
            .button-group button {
                width: 200px;
            }
            
            .input-section input {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        function App() {
            const [count, setCount] = React.useState(0);
            const [name, setName] = React.useState('');

            return (
                <div className="App">
                    <header className="App-header">
                        <h1>🚀 React Simple App</h1>
                        <p>Chào mừng bạn đến với ứng dụng React đơn giản!</p>
                        
                        <div className="counter-section">
                            <h2>Bộ đếm: {count}</h2>
                            <div className="button-group">
                                <button onClick={() => setCount(count + 1)}>
                                    Tăng (+)
                                </button>
                                <button onClick={() => setCount(count - 1)}>
                                    Giảm (-)
                                </button>
                                <button onClick={() => setCount(0)}>
                                    Reset
                                </button>
                            </div>
                        </div>

                        <div className="input-section">
                            <h2>Xin chào, {name || 'Bạn'}!</h2>
                            <input
                                type="text"
                                placeholder="Nhập tên của bạn..."
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                            />
                        </div>

                        <div className="info-section">
                            <p>
                                Đây là một ứng dụng React đơn giản với:
                            </p>
                            <ul>
                                <li>✅ Bộ đếm với useState</li>
                                <li>✅ Input form với state</li>
                                <li>✅ CSS styling</li>
                                <li>✅ Responsive design</li>
                            </ul>
                        </div>
                    </header>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
