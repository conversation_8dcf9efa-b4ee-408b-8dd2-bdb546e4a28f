import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');

  return (
    <div className="App">
      <header className="App-header">
        <h1>🚀 React Simple App</h1>
        <p>Chào mừng bạn đến với ứng dụng React đơn giản!</p>
        
        <div className="counter-section">
          <h2>Bộ đếm: {count}</h2>
          <div className="button-group">
            <button onClick={() => setCount(count + 1)}>
              Tăng (+)
            </button>
            <button onClick={() => setCount(count - 1)}>
              Giảm (-)
            </button>
            <button onClick={() => setCount(0)}>
              Reset
            </button>
          </div>
        </div>

        <div className="input-section">
          <h2>Xin chào, {name || 'Bạn'}!</h2>
          <input
            type="text"
            placeholder="Nhập tên của bạn..."
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
        </div>

        <div className="info-section">
          <p>
            Đây là một ứng dụng React đơn giản với:
          </p>
          <ul>
            <li>✅ Bộ đếm với useState</li>
            <li>✅ Input form với state</li>
            <li>✅ CSS styling</li>
            <li>✅ Responsive design</li>
          </ul>
        </div>
      </header>
    </div>
  );
}

export default App;
