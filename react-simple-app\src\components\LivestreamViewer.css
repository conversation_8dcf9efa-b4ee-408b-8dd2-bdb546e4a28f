.livestream-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.livestream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.livestream-header h1 {
  font-size: 2rem;
  margin: 0;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.refresh-button, .retry-button {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.refresh-button:hover, .retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #ee5a24, #ff6b6b);
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  font-size: 1.2rem;
  color: #fff;
  opacity: 0.9;
}

/* Error State */
.error-state {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.error-state h2 {
  color: #ff6b6b;
  margin-bottom: 15px;
}

.error-state p {
  color: #fff;
  margin-bottom: 25px;
  opacity: 0.9;
}

/* No Streams State */
.no-streams-state {
  text-align: center;
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.no-streams-state h2 {
  color: #fff;
  margin-bottom: 15px;
}

.no-streams-state p {
  color: #fff;
  margin-bottom: 25px;
  opacity: 0.9;
}

/* Stream Selector */
.stream-selector {
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stream-selector h3 {
  color: #fff;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.stream-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.stream-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.stream-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.stream-item.active {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.2);
}

.stream-info {
  color: #fff;
}

.stream-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stream-status {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Video Player */
.video-player-container {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-info {
  margin-bottom: 20px;
  text-align: center;
}

.video-info h2 {
  color: #fff;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.video-info p {
  color: #fff;
  opacity: 0.8;
  font-size: 0.9rem;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .livestream-container {
    padding: 15px;
  }
  
  .livestream-header {
    flex-direction: column;
    text-align: center;
  }
  
  .livestream-header h1 {
    font-size: 1.5rem;
  }
  
  .stream-list {
    grid-template-columns: 1fr;
  }
  
  .stream-selector, .video-player-container {
    padding: 15px;
  }
  
  .video-info h2 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .livestream-header h1 {
    font-size: 1.3rem;
  }
  
  .refresh-button, .retry-button {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}
