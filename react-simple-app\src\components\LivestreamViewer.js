import React, { useState, useEffect } from 'react';
import './LivestreamViewer.css';

const LivestreamViewer = () => {
  const [liveStreams, setLiveStreams] = useState([]);
  const [selectedStream, setSelectedStream] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAllVideos, setShowAllVideos] = useState(false);

  // Cloudflare credentials
  const ACCOUNT_ID = '81e895f7d306ee24edc4f8aee29129e6';
  const API_TOKEN = 'PiwBmbEM6Jh1oQu94HtfXOsy9ADchmZTPELWhBkw';

  // Get video status with appropriate icon and text
  const getVideoStatus = (video) => {
    if (video.status?.state === 'live-inprogress') {
      return '🔴 Đang phát trực tiếp';
    } else if (video.status?.state === 'live') {
      return '🟢 Live sẵn sàng';
    } else if (video.status?.state === 'ready') {
      return '✅ Sẵn sàng xem';
    } else if (video.live === true) {
      return '🔴 Live';
    } else if (video.liveInput && video.liveInput.status === 'connected') {
      return '🟢 Kết nối live';
    } else {
      return '⏸️ Đã ghi';
    }
  };

  // Fetch all videos and filter live ones from Cloudflare Stream API
  const fetchLiveVideos = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `https://api.cloudflare.com/client/v4/accounts/${ACCOUNT_ID}/stream`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${API_TOKEN}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Filter videos based on showAllVideos toggle
        const filteredVideos = data.result.filter(video => {
          if (showAllVideos) {
            // Show all videos including ready ones
            return video.live === true ||
                   video.status?.state === 'live' ||
                   video.status?.state === 'live-inprogress' ||
                   video.status?.state === 'ready' ||
                   (video.liveInput && video.liveInput.status === 'connected');
          } else {
            // Show only live videos
            return video.live === true ||
                   video.status?.state === 'live' ||
                   video.status?.state === 'live-inprogress' ||
                   (video.liveInput && video.liveInput.status === 'connected');
          }
        });

        setLiveStreams(filteredVideos);

        // Auto-select first stream if available
        if (filteredVideos.length > 0 && !selectedStream) {
          setSelectedStream(filteredVideos[0]);
        }
      } else {
        throw new Error(data.errors?.[0]?.message || 'Failed to fetch videos');
      }
    } catch (err) {
      console.error('Error fetching live videos:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch live videos on component mount and set up polling
  useEffect(() => {
    fetchLiveVideos();

    // Poll every 30 seconds to check for new live videos
    const interval = setInterval(fetchLiveVideos, 30000);

    return () => clearInterval(interval);
  }, [showAllVideos]); // Re-fetch when showAllVideos changes

  const handleStreamSelect = (stream) => {
    setSelectedStream(stream);
  };

  const handleRefresh = () => {
    fetchLiveVideos();
  };

  if (loading && liveStreams.length === 0) {
    return (
      <div className="livestream-container">
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Đang tìm kiếm livestream...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="livestream-container">
        <div className="error-state">
          <h2>❌ Lỗi kết nối</h2>
          <p>{error}</p>
          <button onClick={handleRefresh} className="retry-button">
            🔄 Thử lại
          </button>
        </div>
      </div>
    );
  }

  if (liveStreams.length === 0) {
    return (
      <div className="livestream-container">
        <div className="no-streams-state">
          <h2>📺 Không có livestream nào đang phát</h2>
          <p>Hiện tại không có livestream nào đang được phát trực tiếp.</p>
          <button onClick={handleRefresh} className="refresh-button">
            🔄 Làm mới
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="livestream-container">
      <div className="livestream-header">
        <h1>{showAllVideos ? '📺 Tất cả video' : '🔴 Livestream đang phát'}</h1>
        <div className="header-controls">
          <button
            onClick={() => setShowAllVideos(!showAllVideos)}
            className={`toggle-button ${showAllVideos ? 'active' : ''}`}
          >
            {showAllVideos ? '🔴 Chỉ Live' : '📺 Tất cả'}
          </button>
          <button onClick={handleRefresh} className="refresh-button">
            🔄 Làm mới ({liveStreams.length})
          </button>
        </div>
      </div>

      {liveStreams.length > 1 && (
        <div className="stream-selector">
          <h3>Chọn livestream:</h3>
          <div className="stream-list">
            {liveStreams.map((stream) => (
              <button
                key={stream.uid}
                className={`stream-item ${selectedStream?.uid === stream.uid ? 'active' : ''}`}
                onClick={() => handleStreamSelect(stream)}
              >
                <div className="stream-info">
                  <div className="stream-title">
                    {stream.meta?.name || stream.filename || `Video ${stream.uid.substring(0, 8)}`}
                  </div>
                  <div className="stream-status">
                    {getVideoStatus(stream)}
                  </div>
                  <div className="stream-duration">
                    {stream.duration ? `${Math.floor(stream.duration)}s` : 'Live'}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {selectedStream && (
        <div className="video-player-container">
          <div className="video-info">
            <h2>{selectedStream.meta?.name || selectedStream.filename || 'Livestream'}</h2>
            <p>Video ID: {selectedStream.uid}</p>
            <p>Status: {getVideoStatus(selectedStream)}</p>
            {selectedStream.created && (
              <p>Created: {new Date(selectedStream.created).toLocaleString()}</p>
            )}
          </div>

          <div className="video-wrapper">
            <iframe
              src={`https://iframe.videodelivery.net/${selectedStream.uid}`}
              style={{
                border: 'none',
                position: 'absolute',
                top: 0,
                left: 0,
                height: '100%',
                width: '100%',
              }}
              allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
              allowFullScreen={true}
              title="Cloudflare Stream Player"
            ></iframe>
          </div>
        </div>
      )}
    </div>
  );
};

export default LivestreamViewer;
